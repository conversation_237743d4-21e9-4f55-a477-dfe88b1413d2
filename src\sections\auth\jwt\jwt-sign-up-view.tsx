import React, { useState } from 'react';
import { z as zod } from 'zod';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';

import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import { Avatar, Card, ToggleButton, ToggleButtonGroup } from '@mui/material';

import { AppButton } from 'src/components/common';
import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';
import { useAuthContext } from 'src/auth/hooks';
import { signUp } from 'src/auth/context/jwt';
import { CONFIG } from 'src/config-global';

// Schema for Sign Up
export const SignUpSchema = zod.object({
  name: zod.string().min(1, { message: 'Full name is required!' }),
  email: zod
    .string()
    .min(1, { message: 'Email is required!' })
    .email({ message: 'Email must be a valid email address!' }),
  password: zod
    .string()
    .min(1, { message: 'Password is required!' })
    .min(6, { message: 'Password must be at least 6 characters!' }),
});

export type SignUpSchemaType = zod.infer<typeof SignUpSchema>;

export function JwtSignUpView() {
  const router = useRouter();
  const { checkUserSession } = useAuthContext();
  const [errorMsg, setErrorMsg] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState('create'); // Default to 'create'

  const handleChangeTab = (event: React.MouseEvent<HTMLElement>, newAlignment: string) => {
    if (newAlignment !== null) {
      setActiveTab(newAlignment);
      if (newAlignment === 'login') {
        router.push(paths.auth.jwt.signIn);
      }
    }
  };

  const methods = useForm<SignUpSchemaType>({
    resolver: zodResolver(SignUpSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
    },
  });

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      await signUp({
        firstName: data.name,
        lastName: '',
        email: data.email,
        password: data.password,
      });
      await checkUserSession?.();
      router.push(paths.dashboard.root);
    } catch (error) {
      setErrorMsg(error instanceof Error ? error.message : String(error));
    }
  });

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: 456,
        minHeight: '722px',
        height: 'auto',
        mx: 'auto',
        my:'8px',
        bgcolor: 'rgba(244, 244, 244)',
        p: 4,
        borderRadius: 2,
        boxShadow: 3,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
    >
      <Box justifyContent="center" sx={{ mb: 1 }} display="flex">
        <Box
          component="img"
          src={`${CONFIG.site.basePath}/logo/logo-single.svg`}
          sx={{ width: 'auto', height: 44 }}
        />
        <Typography fontWeight="bold" variant="h3" p="6px" lineHeight="32px">
          Workforces
        </Typography>
      </Box>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          mt: 2,
          mb: 0,
          borderRadius: '8px',
          height: '38px',
          backgroundColor: 'rgba(235, 234 ,238)',
          p: 0,
        }}
      >
        <ToggleButtonGroup
          value={activeTab}
          exclusive
          fullWidth
          onChange={handleChangeTab}
          aria-label="Login or Create Account"
          sx={{
            width: '100%',
            gap: 0,
            p: 0.1,
            m: 0,
          }}
        >
          <ToggleButton
            value="login"
            sx={{
              p: 0, m: 0, flex: 1, minWidth: 0, borderRadius: 0,
              color: activeTab === 'login' ? 'rgba(15, 14, 17, 1)' : 'text.disabled',
              backgroundColor: activeTab === 'login' ? 'common.white' : 'rgba(235, 234, 238, 1)',
              '&.Mui-selected': {
                backgroundColor: 'common.white',
                color: 'rgba(15, 14, 17, 1)',
                '&:hover': { backgroundColor: 'white' },
              },
            }}
          >
            Login
          </ToggleButton>
          <ToggleButton
            value="create"
            sx={{
              p: 0, m: 0, flex: 1, minWidth: 0, borderRadius: 0,
              color: activeTab === 'create' ? 'rgba(15, 14, 17, 1)' : 'text.disabled',
              backgroundColor: activeTab === 'create' ? 'common.white' : 'rgba(235, 234, 238, 1)',
              '&.Mui-selected': {
                backgroundColor: 'common.white',
                color: 'rgba(15, 14, 17, 1)',
                '&:hover': { backgroundColor: 'grey.200' },
              },
              '&:hover': {
                backgroundColor: activeTab === 'create' ? 'grey.200' : 'grey.300',
              },
            }}
          >
            Create Account
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      <Typography
        variant="body1"
        fontWeight={400}
        textAlign="start"
        sx={{ my: 3, fontSize: '1.1rem', color: 'rgba(15, 14, 17, 1)' }}
      >
        Create your account to get started
      </Typography>

      {!!errorMsg && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMsg}
        </Alert>
      )}

      <Form methods={methods} onSubmit={onSubmit}>
        <Stack spacing={3}>
          <Card
            sx={{
              border: '1px solid rgba(224, 223, 226, 1)',
              borderRadius: '12px',
              p: 2,
              backgroundColor: 'white',
              display: 'flex',
              flexDirection: 'column',
              minHeight: '215px', // Adjusted for three fields
              width: '100%',
            }}
          >
            <Controller
              name="name"
              control={control}
              defaultValue=""
              render={({ field, fieldState: { error } }) => (
                <Field.Text
                  {...field}
                  label="Full Name"
                  placeholder="Enter your full name"
                  fullWidth
                  variant="standard"
                  error={!!error}
                  helperText={error?.message}
                  InputProps={{
                    disableUnderline: true,
                    startAdornment: (
                      <InputAdornment position="start">
                        <Iconify
                          icon="mdi:account-outline"
                          width={28}
                          sx={{ mr: 2, transform: 'translateY(-15px)', color: 'rgba(144, 108, 229, 1)' }}
                        />
                      </InputAdornment>
                    ),
                    sx: { px: 1, height: 35, borderRadius: 2 },
                  }}
                  InputLabelProps={{
                    style: { fontSize: '1.1rem', color: 'rgba(70, 70, 70, 1)' },
                    sx: { px:10 },
                    shrink: true,
                  }}
                />
              )}
            />
            <Divider sx={{ my: 2 }} />
            <Controller
              name="email"
              control={control}
              defaultValue=""
              render={({ field, fieldState: { error } }) => (
                <Field.Text
                  {...field}
                  label="Email Address"
                  placeholder="Enter valid email address"
                  fullWidth
                  variant="standard"
                  error={!!error}
                  helperText={error?.message}
                  InputProps={{
                    disableUnderline: true,
                    startAdornment: (
                      <InputAdornment position="start">
                        <Iconify
                          icon="mdi:mail-outline"
                          width={28}
                          sx={{ mr: 2, transform: 'translateY(-15px)', color: 'rgba(144, 108, 229, 1)' }}
                        />
                      </InputAdornment>
                    ),
                    sx: { px: 1, height: 35, borderRadius: 2 },
                  }}
                  InputLabelProps={{
                    style: { fontSize: '1.1rem', color: 'rgba(70, 70, 70, 1)' },
                    sx: { px:10 },
                    shrink: true,
                  }}
                />
              )}
            />
            <Divider sx={{ my: 2 }} />
            <Controller
              name="password"
              control={control}
              defaultValue=""
              render={({ field, fieldState: { error } }) => (
                <Field.Text
                  {...field}
                  label="Password"
                  placeholder="••••••••••••••••••••••"
                  type={showPassword ? 'text' : 'password'}
                  fullWidth
                  variant="standard"
                  error={!!error}
                  helperText={error?.message}
                  InputProps={{
                    disableUnderline: true,
                    startAdornment: (
                      <InputAdornment position="start">
                        <Iconify
                          icon="mdi:key-outline"
                          width={28}
                          sx={{ mr: 2, transform: 'translateY(-15px)', color: 'rgba(144, 108, 229, 1)' }}
                        />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton edge="end" onClick={() => setShowPassword(!showPassword)}>
                          <Iconify
                            icon={showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                            width={24}
                            sx={{ transform: 'translateY(-15px)', color: 'rgba(144, 108, 229, 1)' }}
                          />
                        </IconButton>
                      </InputAdornment>
                    ),
                    sx: { px: 1, height: 35, borderRadius: 2 },
                  }}
                  InputLabelProps={{
                    style: { fontSize: '1.1rem', color: 'rgba(70, 70, 70, 1)' },
                    sx: { px:10 },
                    shrink: true,
                  }}
                />
              )}
            />
          </Card>

          <AppButton
            fullWidth
            color="primary"
            size="large"
            type="submit"
            variant="contained"
            isLoading={isSubmitting}
            label="Create Account"
            sx={{
              backgroundColor: 'rgba(155, 94, 231)',
              '&:hover': {
                backgroundColor: 'rgba(155, 94, 231)',
                boxShadow: '0px 5px 5px rgba(0, 0, 0, 0.2)',
              },
              height: '44px',
            }}
          />

          <Stack direction="row" alignItems="center" spacing={2} sx={{}} >
            <Divider sx={{ flexGrow: 1 }}>OR</Divider>
          </Stack>

          <Stack spacing={1}>
            <AppButton
              fullWidth
              variant="outlined"
              color="inherit"
              label="Continue with Apple"
              sx={{ bgcolor: 'rgba(255, 255, 255, 0.7)', height: '44px' }}
              startIcon={
                <Avatar sx={{ bgcolor: 'transparent', width: 24, height: 24 }}>
                  <Iconify icon="logos:apple" />
                </Avatar>
              }
            />
            <AppButton
              fullWidth
              variant="outlined"
              color="inherit"
              label="Continue with Google"
              sx={{ bgcolor: 'rgba(255, 255, 255, 0.7)', height: '44px' }}
              startIcon={
                <Avatar sx={{ bgcolor: 'transparent', width: 24, height: 24 }}>
                  <Iconify icon="logos:google-icon" />
                </Avatar>
              }
            />
            <AppButton
              fullWidth
              variant="outlined"
              color="inherit"
              label="Continue with Facebook"
              sx={{ bgcolor: 'rgba(255, 255, 255, 0.7)', height: '44px' }}
              startIcon={
                <Avatar sx={{ bgcolor: 'transparent', width: 24, height: 24 }}>
                  <Iconify icon="logos:facebook" />
                </Avatar>
              }
            />
          </Stack>
        </Stack>
      </Form>
    </Box>
  );
}
