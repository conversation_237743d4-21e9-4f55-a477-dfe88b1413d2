import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/config-global';

import { SvgColor } from 'src/components/svg-color';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export function useNavData() {
  const { t } = useTranslation();
  const icon = (name: string) => (
    <SvgColor src={`${CONFIG.site.basePath}/assets/icons/navbar/${name}.svg`} />
  );

  const ICONS = {
    job: icon('ic-job'),
    blog: icon('ic-blog'),
    chat: icon('ic-chat'),
    mail: icon('ic-mail'),
    user: icon('ic-user'),
    file: icon('ic-file'),
    lock: icon('ic-lock'),
    tour: icon('ic-tour'),
    order: icon('ic-order'),
    label: icon('ic-label'),
    blank: icon('ic-blank'),
    kanban: icon('ic-kanban'),
    folder: icon('ic-folder'),
    course: icon('ic-course'),
    banking: icon('ic-banking'),
    booking: icon('ic-booking'),
    invoice: icon('ic-invoice'),
    product: icon('ic-product'),
    calendar: icon('ic-calendar'),
    disabled: icon('ic-disabled'),
    external: icon('ic-external'),
    menuItem: icon('ic-menu-item'),
    ecommerce: icon('ic-ecommerce'),
    analytics: icon('ic-analytics'),
    dashboard: icon('ic-dashboard'),
    parameter: icon('ic-parameter'),
  };

  // ----------------------------------------------------------------------

  const arrayOfRoutes = [
    /**
     * Overview
     */
    {
      subheader: ' ',
      items: [
        {
          title: t('pages.dashboard.overView'),
          path: paths.dashboard.root,
          icon: <Iconify icon="icon-park-outline:windows" />,
        },

        // {
        //   title: t('components.navigation.agents'),
        //   path: paths.dashboard.agents.root,
        //   icon: <Iconify icon="material-symbols-light:robot-2-outline-sharp" />,
        // },
        {
          title: t('components.navigation.teams'),
          path: paths.dashboard.teams.root,
          icon: <Iconify icon="mdi:account-group-outline" />,
        },
        {
          title: 'Knowledge Base',
          path: paths.dashboard.knowledgeBase,
          icon: <Iconify icon="icon-park-outline:triangle-round-rectangle" />,
        },
        {
          title: 'Settings',
          path: paths.dashboard.settings,
          icon: <Iconify icon="fa6-regular:chess-queen" />,
        },
        // { title: 'Two', path: paths.dashboard.two, icon: ICONS.ecommerce },
        // { title: 'Three', path: paths.dashboard.three, icon: ICONS.analytics },
      ],
    },
    /**
     * Management
     */
    // {
    //   subheader: 'Management',
    //   items: [
    //     {
    //       title: 'Group',
    //       path: paths.dashboard.group.root,
    //       icon: ICONS.user,
    //       children: [
    //         { title: 'Four', path: paths.dashboard.group.root },
    //         { title: 'Five', path: paths.dashboard.group.five },
    //         { title: 'Six', path: paths.dashboard.group.six },
    //       ],
    //     },
    //   ],
    // },
  ];

  const dashboardNavData = useMemo(() => arrayOfRoutes, [t]);

  return dashboardNavData;
}
