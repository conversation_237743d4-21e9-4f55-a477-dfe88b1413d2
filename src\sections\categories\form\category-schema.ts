import * as z from 'zod';

// Category form validation schema
export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
  color: z.string().optional(),
  icon: z.string().optional(),
  isActive: z.boolean().default(true),
  parentId: z.string().optional(),
  sortOrder: z.number().optional(),
});

export type CategoryFormValues = z.infer<typeof categorySchema>;

// Default values for category form
export const defaultCategoryValues: Partial<CategoryFormValues> = {
  name: '',
  description: '',
  color: '#1976d2',
  icon: 'eva:folder-outline',
  isActive: true,
  sortOrder: 0,
};
