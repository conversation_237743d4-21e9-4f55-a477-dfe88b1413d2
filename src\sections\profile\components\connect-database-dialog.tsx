import { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Button,
  IconButton,
  InputAdornment,
  Alert,
  Grid,
  Stack,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

// Define the form schema with validation rules
const connectDatabaseSchema = z.object({
  databaseName: z.string().min(1, 'Database name is required'),
  host: z.string().min(1, 'Host is required'),
  port: z.string().min(1, 'Port is required'),
  authType: z.string().min(1, 'Authentication type is required'),
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  database: z.string().min(1, 'Database is required'),
  url: z.string().min(1, 'URL is required'),
});

type ConnectDatabaseFormValues = z.infer<typeof connectDatabaseSchema>;

// ----------------------------------------------------------------------

type ConnectDatabaseDialogProps = {
  open: boolean;
  onClose: () => void;
};

export default function ConnectDatabaseDialog({ open, onClose }: ConnectDatabaseDialogProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showError, setShowError] = useState(false);

  // Initialize form with default values
  const methods = useForm<ConnectDatabaseFormValues>({
    resolver: zodResolver(connectDatabaseSchema),
    defaultValues: {
      databaseName: 'Students Data',
      host: '***********',
      port: '3306',
      authType: 'User & Password',
      username: 'my_username',
      password: '••••••••••••',
      database: 'students_data.sql',
      url: 'url2345678912345678',
    },
  });

  const { handleSubmit } = methods;

  const handleTogglePassword = () => {
    setShowPassword((prev) => !prev);
  };

  const onSubmit = async (data: ConnectDatabaseFormValues) => {
    try {
      console.log('Form data submitted:', data);
      // Simulate API call error
      setShowError(true);
      // In a real app, you would make an API call here
    } catch (error) {
      console.error('Error connecting to database:', error);
      setShowError(true);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      PaperProps={{
        sx: {
          borderRadius: 4,
          width: { xs: '90%', sm: '600px' },
          background: (theme) => theme.palette.background.neutral,
          mx: 'auto',
        },
      }}
    >
      {/* Close button */}
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: 16,
          top: 16,
          color: 'grey.500',
          bgcolor: 'grey.100',
          '&:hover': {
            bgcolor: 'grey.200',
          },
          width: 36,
          height: 36,
          zIndex: 1,
        }}
      >
        <Iconify icon="eva:close-fill" width={20} height={20} />
      </IconButton>

      {/* Dialog content */}
      <DialogTitle sx={{ textAlign: 'center', pt: 4, pb: 2 }}>
        <Typography variant="h3" component="div">
          Connect to Database
        </Typography>
      </DialogTitle>

      <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 4 }}>
        Securely link your database from different types
      </Typography>

      <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={3} padding="24px">
          {/* Database Name */}
          <Box>
            <Field.Text name="databaseName" label="Database Name" />
          </Box>

          {/* Host and Port */}
          <Grid container spacing={2}>
            <Grid item xs={8}>
              <Field.Text name="host" label="Host" />
            </Grid>
            <Grid item xs={4}>
              <Field.Text name="port" label="Port" type="number" />
            </Grid>
          </Grid>

          {/* Authentication */}
          <Box>
            {/* <Field.Select
                name="authType"
                label="Authentication"
                options={[{ value: 'User & Password', label: 'User & Password' }]}
              /> */}
          </Box>

          {/* Username */}
          <Box>
            <Field.Text name="username" label="User" />
          </Box>

          {/* Password */}
          <Box>
            <Field.Text
              name="password"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={handleTogglePassword} edge="end">
                      <Iconify icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          {/* Database */}
          <Box>
            {/* <Field.Select
                name="database"
                label="Database"
                options={[{ value: 'students_data.sql', label: 'students_data.sql' }]}
              /> */}
          </Box>

          {/* URL */}
          <Box>
            <Field.Text name="url" label="URL" />
          </Box>

          {showError && (
            <Alert
              severity="error"
              onClose={() => setShowError(false)}
              sx={{
                borderRadius: 1,
                backgroundColor: 'rgba(255, 86, 48, 0.08)',
              }}
            >
              Error occurred please double-check your credentials
            </Alert>
          )}
        </Stack>

        <DialogActions
          sx={{
            width: '100%',
            justifyContent: 'end',
            background: '#E6E5E9',
            mt: '24px',

            borderTop: '1px solid #CDCAD6',
          }}
        >
          <AppButton
            variant="outlined"
            color="inherit"
            onClick={onClose}
            label="Back"
            fullWidth={false}
            size="small"
          />

          <AppButton
            type="submit"
            variant="contained"
            color="primary"
            label="Connect"
            fullWidth={false}
            size="small"
          />
        </DialogActions>
      </Form>
    </Dialog>
  );
}
