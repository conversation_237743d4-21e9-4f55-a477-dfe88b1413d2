import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

const teamTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  flowControl: z.enum(['auto', 'manual']),
  instructions: z.string().optional(),
  selectedAgents: z.array(z.string()).optional(),
  frequency: z.string().optional(),
  startDate: z.date().optional(),
});
export type TeamTemplateFormValues = z.infer<typeof teamTemplateSchema>;
const useTeamTemplateForm = () => {
  const methods = useForm<TeamTemplateFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamTemplateSchema),
  });

  const {
    handleSubmit,
    trigger,
    formState: { isSubmitting },
  } = methods;

  const FREQUENCY_OPTIONS = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'biweekly', label: 'Bi-weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
  ];
  return {
    methods,
    FREQUENCY_OPTIONS,
  };
};

export default useTeamTemplateForm;
