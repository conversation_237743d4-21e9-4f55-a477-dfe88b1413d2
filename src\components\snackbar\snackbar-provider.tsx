import { ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Notista<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'notistack';
import { alpha, styled } from '@mui/material/styles';
import { Box, IconButton } from '@mui/material';
import { Iconify } from '../iconify';

// ----------------------------------------------------------------------

// Create a styled version of NotistackProvider
const StyledSnackbarProvider = styled(NotistackProvider)(({ theme }) => ({
  '& .SnackbarContent-root': {
    width: '100%',
    padding: '8px 16px',
    margin: '8px 0',
    boxShadow: `0 8px 16px 0 ${alpha('#919EAB', 0.16)}`,
    borderRadius: '8px',
    color: theme.palette.grey[800],
    backgroundColor: theme.palette.common.white,
    '&.SnackbarItem-variantSuccess, &.SnackbarItem-variantError, &.SnackbarItem-variantWarning, &.SnackbarItem-variantInfo':
      {
        color: theme.palette.common.white,
        backgroundColor: theme.palette.grey[800],
      },
  },
  '& .SnackbarItem-message': {
    padding: 0,
    fontWeight: 500,
  },
  '& .SnackbarItem-action': {
    marginRight: 0,
    color: 'inherit',
  },
}));

type Props = {
  children: ReactNode;
};

export function SnackbarProvider({ children }: Props) {
  const isRTL = false;

  const onClose = (key: SnackbarKey) => () => {
    console.log('Closing snackbar with key:', key);
  };

  const action = (key: SnackbarKey) => (
    <IconButton size="small" onClick={onClose(key)} sx={{ p: 0.5 }}>
      <Iconify icon="eva:close-fill" />
    </IconButton>
  );

  return (
    <StyledSnackbarProvider
      maxSnack={5}
      autoHideDuration={3000}
      variant="success" // Set default variant
      anchorOrigin={{ vertical: 'bottom', horizontal: isRTL ? 'left' : 'right' }}
      iconVariant={{
        info: <Iconify icon="eva:info-fill" sx={{ mr: 1 }} />,
        success: <Iconify icon="eva:checkmark-circle-2-fill" sx={{ mr: 1 }} />,
        warning: <Iconify icon="eva:alert-triangle-fill" sx={{ mr: 1 }} />,
        error: <Iconify icon="eva:alert-circle-fill" sx={{ mr: 1 }} />,
      }}
      Components={{
        default: Box,
        info: Box,
        success: Box,
        warning: Box,
        error: Box,
      }}
      action={action}
    >
      {children}
    </StyledSnackbarProvider>
  );
}
