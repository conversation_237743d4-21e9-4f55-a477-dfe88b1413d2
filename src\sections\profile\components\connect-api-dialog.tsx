import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stack,
  TextField,
  InputLabel,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';
import { useTranslation } from 'react-i18next';
import { AppButton } from 'src/components/common';

const schema = z.object({
  url: z.string().url('Invalid URL'),
  method: z.string().min(1, 'Method is required'),
  authType: z.string().min(1, 'Auth type is required'),
});

type FormValues = z.infer<typeof schema>;

interface Header {
  id: string;
  key: string;
  value: string;
  isEditing: boolean;
}

export default function ConnectAPIDialog({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) {
  const { t } = useTranslation();
  const [headers, setHeaders] = useState<Header[]>([]);

  const methods = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      url: '',
      method: 'GET',
      authType: 'None',
    },
  });

  const { handleSubmit } = methods;

  const onSubmit = (data: FormValues) => {
    console.log('Submitted:', data, headers);
    onClose();
  };

  const handleAddHeader = () => {
    setHeaders([
      ...headers,
      {
        id: `header-${Date.now()}`,
        key: '',
        value: '',
        isEditing: true,
      },
    ]);
  };

  const handleRemoveHeader = (id: string) => {
    setHeaders(headers.filter((h) => h.id !== id));
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      PaperProps={{
        sx: {
          borderRadius: 4,

          background: (theme) => theme.palette.background.neutral,
          mx: 'auto',
        },
      }}
    >
      <DialogTitle sx={{ textAlign: 'center' }}>
        <Typography variant="h3">Connect API</Typography>
      </DialogTitle>

      <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={2} padding="24px">
          <Box>
            <Typography>URL</Typography>
            <Field.Text name="url" placeholder="URL" />
          </Box>
          <Box>
            <Field.Select
              name="methods"
              sx={{ backgroundColor: 'white' }}
              label="choose the method"
              options={[
                { value: 'Get', label: 'Get' },
                { value: 'Post', label: 'Post' },
              ]}
            />
          </Box>
          <Box>
            <Field.Select
              name="authenticationType"
              label="choose the authetication type"
              sx={{ backgroundColor: 'white' }}
              options={[
                { value: 'Get', label: 'Get' },
                { value: 'Post', label: 'Post' },
              ]}
            />
          </Box>

          <TableContainer>
            <Table size="small" sx={{ border: '1px solid #E0DFE2' }}>
              <TableHead>
                <TableRow>
                  <TableCell>Header key</TableCell>
                  <TableCell>Header value</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody sx={{ backgroundColor: 'white' }}>
                {headers.map((header) => (
                  <TableRow key={header.id}>
                    <TableCell>
                      <TextField
                        value={header.key}
                        onChange={(e) =>
                          setHeaders(
                            headers.map((h) =>
                              h.id === header.id ? { ...h, key: e.target.value } : h
                            )
                          )
                        }
                        size="small"
                        fullWidth
                      />
                    </TableCell>
                    <TableCell>
                      <TextField
                        value={header.value}
                        onChange={(e) =>
                          setHeaders(
                            headers.map((h) =>
                              h.id === header.id ? { ...h, value: e.target.value } : h
                            )
                          )
                        }
                        size="small"
                        fullWidth
                      />
                    </TableCell>
                    <TableCell>
                      <AppButton
                        label="remove"
                        variant="outlined"
                        color="error"
                        onClick={() => handleRemoveHeader(header.id)}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <AppButton
            label="Add header"
            startIcon={<Iconify icon="eva:plus-fill" />}
            onClick={handleAddHeader}
            variant="text"
            sx={{ display: 'flex', justifyContent: 'flex-end' }}
          />
        </Stack>

        <DialogActions
          sx={{
            width: '100%',
            justifyContent: 'end',
            background: '#E6E5E9',
            mt: '24px',

            borderTop: '1px solid #CDCAD6',
          }}
        >
          <AppButton
            onClick={onClose}
            label="Cancel"
            color="inherit"
            variant="outlined"
            fullWidth={false}
            size="small"
          />
          <AppButton
            type="submit"
            variant="contained"
            color="primary"
            label="Connect"
            fullWidth={false}
            size="small"
          />
        </DialogActions>
      </Form>
    </Dialog>
  );
}
