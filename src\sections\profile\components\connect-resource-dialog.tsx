import { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Grid,
  Box,
  Button,
  IconButton,
  Card,
  styled,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

const StyledCard = styled(Card)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius,
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  height: '100%',
  minHeight: 140,
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.customShadows.z8,
  },
}));

// ----------------------------------------------------------------------

type CloudPlatform = {
  id: string;
  name: string;
  icon: string;
  connected: boolean;
};

type ConnectResourceDialogProps = {
  open: boolean;
  onClose: () => void;
};

export default function ConnectResourceDialog({ open, onClose }: ConnectResourceDialogProps) {
  // Mock data for cloud platforms
  const CLOUD_PLATFORMS: CloudPlatform[] = [
    {
      id: 'google-drive',
      name: 'Google Drive',
      icon: '/assets/icons/platforms/ic_google_drive.svg',
      connected: true,
    },
    {
      id: 'mega-drive',
      name: 'Mega Drive',
      icon: '/assets/icons/platforms/ic_mega.svg',
      connected: true,
    },
    {
      id: 'dropbox',
      name: 'Dropbox',
      icon: '/assets/icons/platforms/ic_dropbox.svg',
      connected: true,
    },
    {
      id: 'onedrive',
      name: 'OneDrive',
      icon: '/assets/icons/platforms/ic_onedrive.svg',
      connected: true,
    },
    {
      id: 'icloud',
      name: 'iCloud',
      icon: '/assets/icons/platforms/ic_apple.svg',
      connected: false,
    },
    {
      id: 'amazon-drive',
      name: 'Amazon Drive',
      icon: '/assets/icons/platforms/ic_amazon.svg',
      connected: false,
    },
  ];

  const handleConnect = (platformId: string) => {
    console.log(`Connecting to ${platformId}`);
    // Implement connection logic here
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      PaperProps={{
        sx: {
          borderRadius: 4,

          width: { xs: '90%', sm: '600px' },
          mx: 'auto',
        },
      }}
    >
      {/* Close button */}
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: 16,
          top: 16,
          color: 'grey.500',
          bgcolor: 'grey.100',
          '&:hover': {
            bgcolor: 'grey.200',
          },
          width: 36,
          height: 36,
          zIndex: 1,
        }}
      >
        <Iconify icon="eva:close-fill" width={20} height={20} />
      </IconButton>

      {/* Dialog content */}
      <DialogTitle sx={{ textAlign: 'center', pt: 4, pb: 2 }}>
        <Typography variant="h3" component="div">
          Connect New Resource
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 4 }}>
          Securely link your cloud storage accounts to access and manage your files seamlessly in
          one place.
        </Typography>

        <Grid container spacing={2}>
          {CLOUD_PLATFORMS.map((platform) => (
            <Grid item xs={12} sm={4} key={platform.id}>
              <StyledCard
                sx={{
                  bgcolor: 'background.neutral',
                  ...(platform.connected && {
                    bgcolor: 'background.neutral',
                  }),
                  ...(!platform.connected && {
                    bgcolor: 'white',
                    border: '1px solid',
                    borderColor: 'divider',
                  }),
                }}
                onClick={() => {
                  if (!platform.connected) {
                    handleConnect(platform.id);
                  }
                }}
              >
                <Box
                  component="img"
                  src={platform.icon}
                  alt={platform.name}
                  sx={{ width: 48, height: 48, mb: 2 }}
                />
                <Typography variant="subtitle1" sx={{ mb: 0.5 }}>
                  {platform.name}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: platform.connected ? 'success.main' : 'warning.main',
                    fontWeight: 'bold',
                  }}
                >
                  {platform.connected ? 'Connected' : 'Connect'}
                </Typography>
              </StyledCard>
            </Grid>
          ))}
        </Grid>
      </DialogContent>

      <DialogActions
        sx={{
          width: '100%',
          justifyContent: 'end',
          background: (theme) => theme.palette.background.neutral,
          mt: '24px',

          borderTop: '1px solid #E1DFE4',
        }}
      >
        <AppButton
          variant="outlined"
          color="inherit"
          onClick={onClose}
          label="Cancel"
          fullWidth={false}
          size="small"
        />
      </DialogActions>
    </Dialog>
  );
}
