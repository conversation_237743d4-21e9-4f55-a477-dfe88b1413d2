import { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Button,
  IconButton,
  Stack,
  styled,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import ConnectAPIDialog from './connect-api-dialog';

// ----------------------------------------------------------------------

const StyledAPIItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.background.neutral,
}));

const DisconnectButton = styled(Button)(({ theme }) => ({
  backgroundColor: 'rgba(255, 86, 48, 0.08)',
  color: theme.palette.error.main,
  '&:hover': {
    backgroundColor: 'rgba(255, 86, 48, 0.16)',
  },
}));

// ----------------------------------------------------------------------

type API = {
  id: string;
  name: string;
  icon: string;
};

type APIsDialogProps = {
  open: boolean;
  onClose: () => void;
};

export default function APIsDialog({ open, onClose }: APIsDialogProps) {
  const [connectDialogOpen, setConnectDialogOpen] = useState(false);

  // Mock data for connected APIs
  const CONNECTED_APIS: API[] = [
    {
      id: 'api-1',
      name: 'icanhazdadjoke',
      icon: '😂',
    },
    {
      id: 'api-2',
      name: 'Agify API',
      icon: '🔴',
    },
    {
      id: 'api-3',
      name: 'OpenWeather',
      icon: '🔶',
    },
  ];

  const handleDisconnect = (apiId: string) => {
    console.log(`Disconnecting API ${apiId}`);
    // Implement disconnect logic here
  };

  const handleOpenConnectDialog = () => {
    setConnectDialogOpen(true);
  };

  const handleCloseConnectDialog = () => {
    setConnectDialogOpen(false);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: 4,

            width: { xs: '90%', sm: '600px' },
            mx: 'auto',
          },
        }}
      >
        {/* Close button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 16,
            top: 16,
            color: 'grey.500',
            bgcolor: 'grey.100',
            '&:hover': {
              bgcolor: 'grey.200',
            },
            width: 36,
            height: 36,
            zIndex: 1,
          }}
        >
          <Iconify icon="eva:close-fill" width={20} height={20} />
        </IconButton>

        {/* Dialog content */}
        <DialogTitle sx={{ textAlign: 'center', pt: 4, pb: 2 }}>
          <Typography variant="h3" component="div">
            APIs
          </Typography>
        </DialogTitle>

        <DialogContent>
          <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 4 }}>
            Here&apos;s a list of your connected APIs and you can add more
          </Typography>

          {CONNECTED_APIS.map((api) => (
            <StyledAPIItem key={api.id}>
              <Stack direction="row" spacing={2} alignItems="center">
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '24px',
                  }}
                >
                  {api.icon}
                </Box>
                <Typography variant="subtitle1">{api.name}</Typography>
              </Stack>
              <DisconnectButton
                onClick={() => handleDisconnect(api.id)}
                variant="text"
                size="small"
              >
                Disconnect
              </DisconnectButton>
            </StyledAPIItem>
          ))}
        </DialogContent>

        <DialogActions
          sx={{
            width: '100%',
            justifyContent: 'end',
            background: (theme) => theme.palette.background.neutral,
            mt: '24px',

            borderTop: '1px solid #E1DFE4',
          }}
        >
          <AppButton
            variant="outlined"
            color="inherit"
            onClick={onClose}
            label="Cancel"
            fullWidth={false}
            size="small"
          />

          <AppButton
            variant="contained"
            color="primary"
            onClick={handleOpenConnectDialog}
            label="Add New"
            fullWidth={false}
            size="small"
          />
        </DialogActions>
      </Dialog>

      {/* Connect API Dialog */}
      <ConnectAPIDialog open={connectDialogOpen} onClose={handleCloseConnectDialog} />
    </>
  );
}
