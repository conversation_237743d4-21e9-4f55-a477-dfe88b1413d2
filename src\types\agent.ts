export interface Agent {
  id: string;
  name: string;
  avatar: string;
  role?: string;
}

export const MOCK_AGENTS: Agent[] = [
  {
    id: '1',
    name: '<PERSON>',
    avatar: '/assets/images/avatar/avatar-1.jpg',
    role: 'Team Lead',
  },
  {
    id: '2',
    name: '<PERSON>',
    avatar: '/assets/images/avatar/avatar-2.jpg',
    role: 'Developer',
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON>',
    avatar: '/assets/images/avatar/avatar-3.jpg',
    role: 'Designer',
  },
  {
    id: '4',
    name: '<PERSON>',
    avatar: '/assets/images/avatar/avatar-4.jpg',
    role: 'Product Manager',
  },
  {
    id: '5',
    name: '<PERSON>',
    avatar: '/assets/images/avatar/avatar-5.jpg',
    role: 'QA Engineer',
  },
];
