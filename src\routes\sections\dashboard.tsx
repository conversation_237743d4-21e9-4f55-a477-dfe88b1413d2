import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { DashboardLayout } from 'src/layouts/dashboard';
import { ProfileLayout } from 'src/layouts/profile/layout';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';
import { paths } from '../paths';

// ----------------------------------------------------------------------

const IndexPage = lazy(() => import('src/pages/dashboard/one'));
const TeamsPage = lazy(() => import('src/pages/dashboard/teams/teams-page'));
const TeamTemplateFormPage = lazy(
  () => import('src/pages/dashboard/teams/team-template-form-page')
);
const ProfilePage = lazy(() => import('src/pages/dashboard/profile/profile-page'));
const KnowledgeBasePage = lazy(() => import('src/pages/dashboard/profile/knowledge-base-page'));
const PreferencesPage = lazy(() => import('src/pages/dashboard/profile/preferences-page'));
const SettingsPage = lazy(() => import('src/pages/dashboard/profile/settings-page'));

// const PageTwo = lazy(() => import('src/pages/dashboard/two'));

// ----------------------------------------------------------------------

const layoutContent = (
  <DashboardLayout>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayout>
);

export const dashboardRoutes = [
  {
    path: 'dashboard',
    element: CONFIG.auth.skip ? <>{layoutContent}</> : <AuthGuard>{layoutContent}</AuthGuard>,
    children: [
      { element: <IndexPage />, index: true },
      { path: 'teams', element: <TeamsPage /> },
      { path: 'teams/create', element: <TeamTemplateFormPage /> },
      { path: 'teams/use-template/:id', element: <TeamTemplateFormPage /> },
      {
        path: 'knowledge-base',
        element: <KnowledgeBasePage />,
      },
      {
        path: 'settings',
        element: <SettingsPage />,
      },
      // { path: 'two', element: <PageTwo /> },
      // { path: 'three', element: <PageThree /> },
      // {
      //   path: 'group',
      //   children: [
      //     { element: <PageFour />, index: true },
      //     { path: 'five', element: <PageFive /> },
      //     { path: 'six', element: <PageSix /> },
      //   ],
      // },
    ],
  },
  // {
  //   path: 'dashboard/profile',
  //   element: CONFIG.auth.skip ? (
  //     <ProfileLayout>
  //       <Suspense fallback={<LoadingScreen />}>
  //         <Outlet />
  //       </Suspense>
  //     </ProfileLayout>
  //   ) : (
  //     <AuthGuard>
  //       <ProfileLayout>
  //         <Suspense fallback={<LoadingScreen />}>
  //           <Outlet />
  //         </Suspense>
  //       </ProfileLayout>
  //     </AuthGuard>
  //   ),
  //   children: [
  //     { element: <ProfilePage />, index: true },
  //     { path: 'knowledge-base', element: <KnowledgeBasePage /> },
  //     { path: 'preferences', element: <PreferencesPage /> },
  //     { path: 'settings', element: <SettingsPage /> },
  //   ],
  // },
];
